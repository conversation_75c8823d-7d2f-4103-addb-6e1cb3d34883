---
name: Build a React app with Bun
---

Bun supports `.jsx` and `.tsx` files out of the box. React just works with B<PERSON>.

Create a new React app with `bun init --react`. This gives you a template with a simple React app and a simple API server together in one full-stack app.

```bash
# Create a new React app
$ bun init --react

# Run the app in development mode
$ bun dev

# Build as a static site for production
$ bun run build

# Run the server in production
$ bun start
```

---

### Hot Reloading

Run `bun dev` to start the app in development mode. This will start the API server and the React app with hot reloading.

### Full-Stack App

Run `bun start` to start the API server and frontend together in one process.

### Static Site

Run `bun run build` to build the app as a static site. This will create a `dist` directory with the built app and all the assets.

```
├── src/
│   ├── index.tsx       # Server entry point with API routes
│   ├── frontend.tsx    # React app entry point with HMR
│   ├── App.tsx         # Main React component
│   ├── APITester.tsx   # Component for testing API endpoints
│   ├── index.html      # HTML template
│   ├── index.css       # Styles
│   └── *.svg           # Static assets
├── package.json        # Dependencies and scripts
├── tsconfig.json       # TypeScript configuration
├── bunfig.toml         # Bun configuration
└── bun.lock            # Lock file
```
