# AGENSI

Project dengan Tailwind CSS CLI

## Setup

Tailwind CSS CLI sudah diinstall dan dikonfigurasi dalam project ini.

## File Structure

```
├── src/
│   └── input.css          # Input CSS dengan Tailwind directives
├── dist/
│   └── output.css         # Output CSS yang di-generate
├── tailwind.config.js     # Konfigurasi Tailwind CSS
├── tailwindcss.exe        # Tailwind CSS CLI executable
└── index.html             # Contoh file HTML
```

## Commands

### Build CSS
```bash
npm run build-css
```
atau
```bash
./tailwindcss.exe -i ./src/input.css -o ./dist/output.css
```

### Watch Mode (Auto-rebuild)
```bash
npm run watch-css
```
atau
```bash
./tailwindcss.exe -i ./src/input.css -o ./dist/output.css --watch
```

## Penggunaan

1. Edit file `src/input.css` untuk menambahkan custom CSS
2. Gunakan Tailwind classes di file HTML Anda
3. Jalankan `npm run build-css` untuk generate CSS
4. Link file `dist/output.css` di HTML Anda

## Contoh

Lihat `index.html` untuk contoh penggunaan Tailwind CSS classes.

## Install Dependencies

```bash
bun install
```

To run:

```bash
bun run index.js
```

This project was created using `bun init` in bun v1.2.15. [Bun](https://bun.sh) is a fast all-in-one JavaScript runtime.
