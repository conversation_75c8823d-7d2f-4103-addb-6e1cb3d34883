{"name": "agensi", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build-css": "tailwindcss -i ./src/input.css -o ./dist/output.css", "watch-css": "tailwindcss -i ./src/input.css -o ./dist/output.css --watch"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "@types/bun": "latest"}, "private": true, "peerDependencies": {"typescript": "^5"}}