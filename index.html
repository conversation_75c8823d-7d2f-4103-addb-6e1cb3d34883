<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fajri - Digital Creator Portfolio</title>
    <link href="/dist/output.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0f172a;
            color: #cbd5e1;
            scroll-behavior: smooth;
        }
        .hero-gradient {
            background: radial-gradient(circle at 70% 30%, rgba(30, 41, 59, 0.5) 0%, rgba(15, 23, 42, 0) 70%);
        }
        .profile-glow {
            box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3), 0 0 20px rgba(59, 130, 246, 0.5);
        }
        .glass-card {
            background: rgba(30, 41, 59, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(71, 85, 105, 0.5);
            transition: all 0.3s ease;
        }
        .glass-card:hover {
            border-color: rgba(59, 130, 246, 0.7);
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
            transform: translateY(-5px);
        }
        .service-card:hover {
            border-color: #3B82F6;
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
        }
        .nav-link {
            position: relative;
        }
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: #3B82F6;
            transition: width 0.3s ease;
        }
        .nav-link:hover::after {
            width: 100%;
        }
    </style>
</head>
<body class="bg-slate-900 text-slate-300">
    <!-- Navbar -->
    <nav class="fixed w-full z-50 bg-slate-900/80 backdrop-blur-sm border-b border-slate-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="#" class="text-xl font-bold text-slate-100 flex items-center">
                        <span class="text-electric-blue">NUSA</span>FLOW
                    </a>
                </div>
                
                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="nav-link text-slate-300 hover:text-white">Beranda</a>
                    <a href="#portfolio" class="nav-link text-slate-300 hover:text-white">Portfolio</a>
                    <a href="#services" class="nav-link text-slate-300 hover:text-white">Layanan</a>
                    <a href="#about" class="nav-link text-slate-300 hover:text-white">Tentang</a>
                    <a href="#contact" class="px-4 py-2 bg-electric-blue text-white rounded-md hover:bg-electric-blue-dark transition">Hubungi</a>
                </div>
                
                <!-- Mobile Menu Button -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-slate-300 hover:text-white focus:outline-none">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-slate-800">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="#home" class="block px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-700">Beranda</a>
                <a href="#portfolio" class="block px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-700">Portfolio</a>
                <a href="#services" class="block px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-700">Layanan</a>
                <a href="#about" class="block px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-700">Tentang</a>
                <a href="#contact" class="block px-3 py-2 rounded-md text-base font-medium text-white bg-electric-blue">Hubungi</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="pt-24 pb-16 md:pt-32 md:pb-24 hero-gradient">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row items-center">
                <!-- Left Column -->
                <div class="md:w-1/2 mb-12 md:mb-0">
                    <div class="text-sm font-medium text-electric-blue mb-4 animate-pulse-slow">FAJRI - DIGITAL CREATOR</div>
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-100 leading-tight mb-6">
                        Wujudkan Visi Bisnis Anda <span class="text-electric-blue">di Dunia Digital</span>.
                    </h1>
                    <p class="text-lg text-slate-300 mb-8 max-w-lg">Saya Fajri, pendiri NUSAFLOW. Misi saya adalah membantu bisnis Anda memiliki 'rumah digital' pertama yang profesional, modern, dan terjangkau.
                    </p>
                    <div class="flex flex-wrap gap-4">
                        <a href="#portfolio" class="px-8 py-3 bg-electric-blue text-white font-medium rounded-md hover:bg-electric-blue-dark transition transform hover:-translate-y-1 shadow-glow">
                            Lihat Karya Saya
                        </a>
                        <a href="#contact" class="px-8 py-3 border border-slate-600 text-slate-300 font-medium rounded-md hover:bg-slate-800 transition">
                            Hubungi Saya
                        </a>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative">
                        <div class="absolute -inset-4 rounded-full bg-electric-blue opacity-20 blur-xl animate-pulse"></div>
                        <div class="relative w-64 h-64 md:w-80 md:h-80 rounded-full overflow-hidden border-4 border-slate-800 profile-glow">
                            <img 
                                  src="/image/avatar.png"
                                  alt="Foto Profil"
                                  class="w-full h-full object-cove" >
                        </div>
                        <div class="absolute top-0 right-0 w-16 h-16 bg-electric-blue rounded-full flex items-center justify-center animate-float">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<!-- Portfolio Section -->
<section id="portfolio" class="py-16 md:py-24 bg-slate-900/50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-slate-100 mb-4">Proyek Unggulan</h2>
            <p class="text-slate-400 max-w-2xl mx-auto">Solusi digital yang saya kembangkan untuk membantu klien mencapai tujuan bisnis mereka</p>
        </div>
        
        <!-- Kartu Portofolio Utama -->
        <div class="max-w-4xl mx-auto">
            <div class="glass-card rounded-2xl overflow-hidden">
                <div class="p-1 bg-gradient-to-r from-slate-800 to-slate-900 rounded-2xl">
                    <div class="bg-slate-800 p-6 rounded-xl">
                        <div class="border-2 border-slate-700 rounded-xl w-full max-h-[500px] mb-6 overflow-hidden">
                            <img 
                              src="/image/mockup.png"
                              alt="Mockup Kopi Senja"
                              class="w-full h-full object-contain">
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-slate-100 mb-2">Kopi Senja - Landing Page Kedai Kopi</h3>
                            <p class="text-slate-400 mb-6">
                             Sebuah proyek konseptual yang bertujuan merancang identitas digital untuk brand kopi 'Kopi Senja'. Fokusnya adalah menciptakan landing page yang profesional, menangkap esensi brand yang hangat, dan menyederhanakan alur pemesanan melalui integrasi WhatsApp.
                            </p>
                            <div class="flex flex-wrap gap-4">
                                <a href="senja.html" class="px-6 py-2 border border-slate-600 text-slate-300 font-medium rounded-md hover:bg-slate-700/50 transition">
                                    Lihat Situs Live
                                </a>
                                <!-- TOMBOL UBAHAN (Hybrid Approach) -->
                                <a href="#kopi-senja-details" class="px-6 py-2 border border-electric-blue/50 text-electric-blue font-medium rounded-md hover:bg-electric-blue/10 transition flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    Ringkasan Studi Kasus
                                </a>
                                <a href="studi.html" class="px-6 py-2 bg-electric-blue/90 text-white font-medium rounded-md hover:bg-electric-blue transition flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    Versi Lengkap
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- ==================== -->
<!-- SECTION RINGKASAN BARU (Tambahkan setelah Portfolio Section) -->
<!-- ==================== -->
<section id="kopi-senja-details" class="py-12 bg-slate-800/20 scroll-mt-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            <h3 class="text-2xl font-bold text-slate-100 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-electric-blue mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Ringkasan Proyek Kopi Senja
            </h3>
            <a href="studi.html" class="text-sm text-electric-blue hover:underline flex items-center">
                Baca studi kasus lengkap
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
            </a>
        </div>

        <!-- Grid Konten Ringkasan -->
        <div class="grid md:grid-cols-2 gap-8">
            <div class="space-y-6">
                <div class="glass-card p-6 rounded-xl">
                    <h4 class="text-lg font-semibold text-electric-blue mb-3">🔍 Tantangan Klien</h4>
                    <ul class="list-disc list-inside text-slate-300 space-y-2 pl-2">
                        <li>Tidak Punya 'Rumah Digital': Promosi hanya tersebar di media sosial tanpa adanya pusat informasi yang profesional.</li>
                        <li>Proses Pemesanan Tidak Efisien: Pemesanan masih manual dan seringkali calon pelanggan bingung dengan menu yang ada.</li>
                        <li>Citra Brand Kurang Kuat: Kesulitan untuk tampil beda dan lebih premium dari pesaing.</li>
                    </ul>
                </div>

                <div class="glass-card p-6 rounded-xl">
                    <h4 class="text-lg font-semibold text-electric-blue mb-3">📅 Timeline</h4>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-slate-300">Desain & Pengembangan</span>
                                <span class="text-slate-400">1 Minggu</span>
                            </div>
                            <div class="w-full bg-slate-700 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 20%"></div>
                            </div>
                        </div>
                        <!-- Tambahkan fase lainnya -->
                    </div>
                </div>
            </div>

<!-- Di dalam section #kopi-senja-details -->
<div class="glass-card p-6 rounded-xl">
    <h4 class="text-lg font-semibold text-electric-blue mb-3">🎯 Hasil yang Dicapai</h4>
    <ul class="space-y-4">
        <li class="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div>
                <span class="font-medium text-slate-100">Citra Brand Lebih Profesional & Terpercaya</span>
                <p class="text-slate-400 text-sm">Desain modern meningkatkan persepsi kualitas produk di mata pelanggan</p>
            </div>
        </li>
        <li class="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div>
                <span class="font-medium text-slate-100">Pusat Informasi Produk 24/7</span>
                <p class="text-slate-400 text-sm">Pelanggan bisa mengakses menu, harga, dan promo kapan saja tanpa batas waktu</p>
            </div>
        </li>
        <li class="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div>
                <span class="font-medium text-slate-100">Alur Pemesanan Lebih Jelas</span>
                <p class="text-slate-400 text-sm">CTA WhatsApp yang strategis mengurangi kebingungan pelanggan dalam memesan</p>
            </div>
        </li>
    </ul>
</div>
                <div class="glass-card p-6 rounded-xl">
                    <h4 class="text-lg font-semibold text-electric-blue mb-3">🛠 Teknologi Digunakan</h4>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 bg-slate-700 rounded-full text-sm">HTML5 & CSS3</span>
                        <span class="px-3 py-1 bg-slate-700 rounded-full text-sm">Tailwind CSS</span>
                        <span class="px-3 py-1 bg-slate-700 rounded-full text-sm">JavaScripts</span>
                         <span class="px-3 py-1 bg-slate-700 rounded-full text-sm">AI Assistant (DeepSeek/ChatGPT)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

    <!-- Services Section -->
    <section id="services" class="py-16 md:py-24 bg-slate-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-slate-100 mb-4">Layanan Unggulan</h2>
                <p class="text-slate-400 max-w-2xl mx-auto">Solusi lengkap untuk transformasi digital bisnis Anda</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Service Card 1 -->
                <div class="glass-card rounded-xl p-6 border border-slate-700 service-card">
                    <div class="w-14 h-14 rounded-full bg-electric-blue/10 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-electric-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-slate-100 mb-3">Landing Page Profesional</h3>
                    <p class="text-slate-400 mb-4">
                        Desain website satu halaman yang fokus untuk menampilkan keunggulan produk Anda dan meyakinkan pengunjung untuk menghubungi Anda.
                    </p>
                    <ul class="text-slate-400 space-y-2 text-sm">
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-electric-blue mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Tampilan Sempurna di HP & Laptop
                        </li>
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-electric-blue mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Struktur Ramah Google (SEO-Friendly)
                        </li>
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-electric-blue mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Siap Dihubungkan ke Alat Analitik
                        </li>
                    </ul>
                </div>
                
                <!-- Service Card 2 -->
                <div class="glass-card rounded-xl p-6 border border-slate-700 service-card">
                    <div class="w-14 h-14 rounded-full bg-electric-blue/10 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-electric-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-slate-100 mb-3">AI Chatbot Solusi (Comming Soon)</h3>
                    <p class="text-slate-400 mb-4">
                        Asisten virtual cerdas yang siap melayani pertanyaan pelanggan di WhatsApp Anda 24/7, bahkan saat Anda sedang sibuk atau tidur.
                    </p>
                    <ul class="text-slate-400 space-y-2 text-sm">
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-electric-blue mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Jawaban Otomatis Sesuai Bisnis Anda
                        </li>
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-electric-blue mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Terhubung Langsung ke WhatsApp Anda
                        </li>
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-electric-blue mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Laporan Interaksi Pelanggan
                        </li>
                    </ul>
                </div>
                
                <!-- Service Card 3 -->
                <div class="glass-card rounded-xl p-6 border border-slate-700 service-card">
                    <div class="w-14 h-14 rounded-full bg-electric-blue/10 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-electric-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-slate-100 mb-3">Copywriting Persuasif</h3>
                    <p class="text-slate-400 mb-4">
                        Susunan kata dan cerita yang dirancang khusus untuk membuat brand Anda lebih menarik dan membuat pelanggan lebih yakin untuk membeli.
                    </p>
                    <ul class="text-slate-400 space-y-2 text-sm">
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-electric-blue mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Teks yang "Bicara" pada Target Pasar Anda
                        </li>
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-electric-blue mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Judul & Kalimat Ajakan yang Menjual
                        </li>
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-electric-blue mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Cerita Brand yang Menarik
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- About Me Section -->
    <section id="about" class="py-16 md:py-24 bg-gradient-to-b from-slate-900 to-slate-800">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="glass-card rounded-2xl overflow-hidden">
                <div class="p-1 bg-gradient-to-r from-slate-800 to-electric-blue/10 rounded-2xl">
                    <div class="bg-slate-800 p-8 md:p-12 rounded-xl">
                        <h2 class="text-3xl md:text-4xl font-bold text-slate-100 mb-6 text-center">Misi Saya</h2>
                        <p class="text-lg text-slate-300 text-center leading-relaxed max-w-3xl mx-auto">
                            Saya percaya teknologi seharusnya mempermudah, bukan mempersulit. Misi saya adalah menjadi jembatan digital bagi para pelaku UMKM; mengubah visi bisnis Anda menjadi sebuah website yang tidak hanya indah, tapi benar-benar bekerja untuk Anda.
                        </p>
                        <div class="mt-10 flex justify-center">
                            <div class="flex items-center space-x-4">
                                <div class="w-16 h-16 rounded-full bg-electric-blue/10 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-electric-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-bold text-slate-100">Fajri</h4>
                                    <p class="text-slate-400">Digital Creator</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Tambahkan ini SEBELUM bagian footer (sebelum <footer id="contact">) -->
<section class="py-12 bg-slate-800/50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h3 class="text-2xl font-bold text-slate-100 mb-6">Butuh Bantuan Cepat?</h3>
    <p class="text-slate-300 mb-8 max-w-2xl mx-auto">
      Hubungi saya langsung via WhatsApp untuk konsultasi gratis atau diskusi proyek Anda.
    </p>
    <a 
      href="https://wa.me/6285797723305" 
      target="_blank"
      class="inline-flex items-center px-8 py-4 bg-green-600 hover:bg-green-700 text-white font-bold rounded-full transition-all shadow-lg hover:shadow-green-500/30 animate-pulse"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
      </svg>
      Chat via WhatsApp
    </a>
  </div>
</section>

<!-- Footer -->
<footer id="contact" class="pt-16 pb-8 border-t border-slate-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
            <div class="mb-6 md:mb-0">
                <a href="#" class="text-xl font-bold text-slate-100 flex items-center">
                    <span class="text-electric-blue">NUSA</span>FLOW
                </a>
                <p class="mt-2 text-slate-500 text-sm">Sebuah studio digital personal oleh Fajri</p>
            </div>
            
            <div class="flex flex-col items-center md:items-end">
                <div class="flex space-x-6 mb-4">
                    <!-- Facebook -->
                    <a href="#" ria-label="Facebook" class="text-slate-400 hover:text-electric-blue transition-colors duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                    </a>
                  
                    <!-- Instagram -->
                    <a href="https://www.instagram.com/muhfajri.id" aria-label="Instagram" class="text-slate-400 hover:text-electric-blue transition-colors duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
                      </svg>
                    </a>
                  
                    <!-- WhatsApp -->
                    <a href="https://wa.me/6285797723305" aria-label="WhatsApp" class="text-slate-400 hover:text-electric-blue transition-colors duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
                      </svg>
                    </a>
                                 
                </div>
                <p class="text-slate-500 text-sm"><EMAIL></p>
            </div>
        </div>
        
        <div class="mt-8 pt-8 border-t border-slate-800 text-center">
            <p class="text-slate-500 text-sm">&copy; 2025 NUSAFLOW. All rights reserved.</p>
        </div>
    </div>
</footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    const mobileMenu = document.getElementById('mobile-menu');
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });
        });

        // Add glow effect on service card hover
        const serviceCards = document.querySelectorAll('.service-card');
        serviceCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.boxShadow = '0 0 15px rgba(59, 130, 246, 0.4)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.boxShadow = '';
            });
        });
    </script>
</body>
</html>