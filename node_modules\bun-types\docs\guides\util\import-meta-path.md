---
name: Get the absolute path of the current file
---

<PERSON><PERSON> provides a handful of module-specific utilities on the [`import.meta`](https://bun.com/docs/api/import-meta) object. Use `import.meta.path` to retrieve the absolute path of the current file.

```ts#/a/b/c.ts
import.meta.path; // => "/a/b/c.ts"
```

---

See [Docs > API > import.meta](https://bun.com/docs/api/import-meta) for complete documentation.
