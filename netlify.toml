[build]
  # Directory to publish (default is root)
  publish = "."
  
  # Build command
  command = "npm run build-css"

[build.environment]
  NODE_VERSION = "18"

# Headers for CSS files
[[headers]]
  for = "/dist/*.css"
  [headers.values]
    Content-Type = "text/css"
    Cache-Control = "public, max-age=31536000"

# Redirect rules (optional)
[[redirects]]
  from = "/porto"
  to = "/"
  status = 301

[[redirects]]
  from = "/porto.html"
  to = "/"
  status = 301
